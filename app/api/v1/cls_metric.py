from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.deps.db import get_db
from app.models.metric import MetricORM, Metric, MetricCreate
from typing import List
from app.services.source_collect.cls_online_users_collect import fetch_peak_online_users, save_peak_online_users
from app.services.source_collect.cls_slow_api_collect import fetch_slow_api_details
from datetime import datetime, timedelta
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException

router = APIRouter()

@router.post("/metric", response_model=Metric, summary="创建指标")
def create_metric(metric: MetricCreate, db: Session = Depends(get_db)):
    db_obj = MetricORM(**metric.dict())
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj

@router.get("/metric/{metric_id}", response_model=Metric, summary="查询单个指标")
def get_metric(metric_id: int, db: Session = Depends(get_db)):
    obj = db.query(MetricORM).filter_by(id=metric_id).first()
    if not obj:
        raise HTTPException(status_code=404, detail="指标不存在")
    return obj

@router.get("/metric", response_model=List[Metric], summary="查询所有指标")
def list_metrics(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    objs = db.query(MetricORM).offset(skip).limit(limit).all()
    return objs

@router.put("/metric/{metric_id}", response_model=Metric, summary="更新指标")
def update_metric(metric_id: int, metric: MetricCreate, db: Session = Depends(get_db)):
    obj = db.query(MetricORM).filter_by(id=metric_id).first()
    if not obj:
        raise HTTPException(status_code=404, detail="指标不存在")
    for k, v in metric.dict().items():
        setattr(obj, k, v)
    db.commit()
    db.refresh(obj)
    return obj

@router.delete("/metric/{metric_id}", summary="删除指标")
def delete_metric(metric_id: int, db: Session = Depends(get_db)):
    obj = db.query(MetricORM).filter_by(id=metric_id).first()
    if not obj:
        raise HTTPException(status_code=404, detail="指标不存在")
    db.delete(obj)
    db.commit()
    return {"msg": "删除成功"}

from pydantic import BaseModel
from fastapi import Body
class ExecuteMetricRequest(BaseModel):
    metric_id: int
    start_time: str
    end_time: str

@router.post("/metric/execute", summary="执行指标")
def execute_metric(
        request_data:ExecuteMetricRequest =
    Body(..., description="执行指标入参"),
    db: Session = Depends(get_db)
):
    """
    执行指定指标ID对应的采集函数，metric_sql作为query参数
    """
    metric = db.query(MetricORM).filter_by(id=request_data.metric_id).first()
    if not metric:
        raise HTTPException(status_code=404, detail="指标不存在")
    # 解析时间
    def parse_time(timestr, is_end=False):
        """
        兼容只传递年月日格式，自动补全为 00:00:00 或 23:59:59（如果需要）
        """
        if not timestr:
            return None
        try:
            # 只传日期
            if len(timestr) == 10:
                if is_end:
                    timestr = timestr + ' 23:59:59'
                else:
                    timestr = timestr + ' 00:00:00'
            return int(datetime.strptime(timestr, '%Y-%m-%d %H:%M:%S').timestamp())
        except Exception:
            return int(datetime.strptime(timestr, '%Y-%m-%d %H:%M:%S.%f').timestamp())
    now = datetime.now()
    st = parse_time(request_data.start_time, False) if request_data.start_time else int((now - timedelta(hours=1)).timestamp())
    et = parse_time(request_data.end_time, True) if request_data.end_time else int(now.timestamp())
    try:
        # 动态分发
        if int(request_data.metric_id) == 1:
            result = fetch_peak_online_users(metric.metric_sql, st, et)
            save_peak_online_users(db, result)
        elif int(request_data.metric_id) == 2:
            result = fetch_slow_api_details(metric.metric_sql, st, et, db)
        else:
            raise HTTPException(status_code=400, detail="暂不支持该指标ID的执行")
    except TencentCloudSDKException as e:
        if 'SearchTimeout' in str(e):
            return {"msg": "CLS查询超时，请稍后重试", "detail": str(e)}
        raise HTTPException(status_code=500, detail=f"CLS查询异常: {e}")
    return {"msg": "执行成功", "result": result} 