from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from app.deps.db import get_db
from app.services.source_collect.cls_interface_vibration_api import collect_core_interface_performance
from pydantic import BaseModel
from typing import Optional
router = APIRouter()

class CollectRequest(BaseModel):
    target_date: str
    url: Optional[str] = None

@router.post('/collect_vibration', summary="采集接口抖动率明细")
def start_collect_vibration_api(
        request_data:CollectRequest = Body(..., description="请求数据"),
        db: Session = Depends(get_db)
):
    """
    手动触发采集核心接口性能数据，并写入数据库
    实现逻辑：
    """
    result = collect_core_interface_performance(target_date=request_data.target_date,db=db,url=request_data.url)
    return result


