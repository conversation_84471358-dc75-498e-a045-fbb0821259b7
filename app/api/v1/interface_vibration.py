from fastapi import APIRouter, Depends, HTTPException, Body, Query
from sqlalchemy.orm import Session
from app.deps.db import get_db
from app.services.source_collect.cls_interface_vibration_api import collect_core_interface_performance
from app.services.metric_calc.interface_vibration_calc import calculate_interface_jitter_exceptions
from app.services.business_services.core_interface_service import (
    add_core_interface,
    update_core_interface,
    delete_core_interface,
    get_core_interface_list,
    get_core_interface_by_id
)
from app.models.core_interface import CoreInterfaceBase, CoreInterfaceORM
from pydantic import BaseModel
from typing import Optional

router = APIRouter()

class CollectRequest(BaseModel):
    target_date: str
    url: Optional[str] = None

class CalculateJitterRequest(BaseModel):
    target_date: Optional[str] = None
    url_path: Optional[str] = None

class AddInterfaceRequest(BaseModel):
    interface_name: str
    url: str
    product_type: str

class UpdateInterfaceRequest(BaseModel):
    interface_name: str


@router.post('/add_core_interface', description='添加接口')
def add_core_interface(
        request_data: CoreInterfaceBase,
        db: Session = Depends(get_db)
):
    obj =db.query(CoreInterfaceORM).filter(CoreInterfaceBase.url == request_data.url).first()
    if obj:
       return {"code":"-1","msg":"接口已存在"}
    db_obj = CoreInterfaceORM(**request_data.dict())
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj



@router.post('/collect_vibration', summary="采集接口抖动率明细")
def start_collect_vibration_api(
        request_data: CollectRequest = Body(..., description="请求数据"),
        db: Session = Depends(get_db)
):
    """
    手动触发采集核心接口性能数据，并写入数据库
    实现逻辑：
    """
    result = collect_core_interface_performance(target_date=request_data.target_date, db=db, url=request_data.url)
    return result


@router.post('/calculate_jitter_exceptions', summary="计算接口抖动异常")
def calculate_jitter_exceptions_api(
        request_data: CalculateJitterRequest = Body(..., description="请求数据"),
        db: Session = Depends(get_db)
):
    """
    从core_interface_performance_detail表计算接口抖动异常，写入core_api_jitter_exception表

    实现逻辑：
    1. 从core_interface_performance_detail表读取抖动数据（变异系数>0.5）
    2. 按接口路径分组，识别连续的抖动时间段
    3. 计算每个抖动事件的开始时间、持续时长
    4. 生成详细的触发异常明细记录
    5. 批量写入core_api_jitter_exception表
    """
    result = calculate_interface_jitter_exceptions(
        db=db,
        target_date=request_data.target_date,
        url_path=request_data.url_path
    )
    return result
