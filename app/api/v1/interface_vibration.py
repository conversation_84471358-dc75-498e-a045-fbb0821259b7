from fastapi import APIRouter, Depends, HTTPException, Body, Query
from sqlalchemy.orm import Session
from app.deps.db import get_db
from app.services.source_collect.cls_interface_vibration_api import collect_core_interface_performance
from app.services.metric_calc.interface_vibration_calc import calculate_interface_jitter_exceptions
from app.services.business_services.core_interface_service import CoreInterfaceService
from app.models.core_interface import (
    CoreInterfaceBase,
    CoreInterfaceORM,
    CollectRequest,
    CalculateJitterRequest,
    AddInterfaceRequest,
    UpdateInterfaceRequest
)

router = APIRouter()

# 创建服务实例
core_interface_service = CoreInterfaceService()


# ==================== 核心接口管理 API ====================

@router.post('/core_interface', summary="新增核心接口")
def add_core_interface_api(
        request_data: AddInterfaceRequest = Body(..., description="接口信息"),
        db: Session = Depends(get_db)
):
    """
    新增核心接口

    业务逻辑：
    1. 如果URL是完整URL（https开头），自动提取path
    2. 对path进行指纹清洗，替换动态参数为*号（如 /a/123/a -> /a/*/a）
    3. 检查是否重复（基于清洗后的URL）
    4. 新增记录

    示例：
    - 输入: https://api.example.com/user/123/profile
    - 处理: /user/123/profile -> /user/*/profile
    - 存储: /user/*/profile
    """
    result = add_core_interface(
        interface_name=request_data.interface_name,
        url=request_data.url,
        product_type=request_data.product_type,
        db=db
    )
    return result


@router.put('/core_interface/{interface_id}', summary="编辑核心接口")
def update_core_interface_api(
        interface_id: int,
        request_data: UpdateInterfaceRequest = Body(..., description="更新信息"),
        db: Session = Depends(get_db)
):
    """
    编辑核心接口（只能编辑名称）

    业务逻辑：
    1. 根据ID查找接口
    2. 只允许修改接口名称
    3. 更新记录
    """
    result = update_core_interface(
        interface_id=interface_id,
        interface_name=request_data.interface_name,
        db=db
    )
    return result


@router.delete('/core_interface/{interface_id}', summary="删除核心接口")
def delete_core_interface_api(
        interface_id: int,
        db: Session = Depends(get_db)
):
    """
    删除核心接口

    业务逻辑：
    1. 根据ID查找接口
    2. 删除记录
    """
    result = delete_core_interface(
        interface_id=interface_id,
        db=db
    )
    return result


@router.get('/core_interface', summary="获取核心接口列表")
def get_core_interface_list_api(
        page: int = Query(1, description="页码", ge=1),
        page_size: int = Query(20, description="每页数量", ge=1, le=100),
        db: Session = Depends(get_db)
):
    """
    获取核心接口列表（分页）

    参数：
    - page: 页码，从1开始
    - page_size: 每页数量，最大100
    """
    result = get_core_interface_list(
        db=db,
        page=page,
        page_size=page_size
    )
    return result


@router.get('/core_interface/{interface_id}', summary="获取核心接口详情")
def get_core_interface_detail_api(
        interface_id: int,
        db: Session = Depends(get_db)
):
    """
    根据ID获取核心接口详情
    """
    result = get_core_interface_by_id(
        interface_id=interface_id,
        db=db
    )
    return result


# ==================== 原有功能 API ====================

# 保留原有接口以兼容现有代码
@router.post('/add_core_interface', description='添加接口（已废弃，请使用 POST /core_interface）')
def add_core_interface_legacy(
        request_data: CoreInterfaceBase,
        db: Session = Depends(get_db)
):
    """兼容性接口，建议使用新的 POST /core_interface"""
    result = add_core_interface(
        interface_name=request_data.interface_name,
        url=request_data.url,
        product_type=request_data.product_type,
        db=db
    )
    return result



@router.post('/collect_vibration', summary="采集接口抖动率明细")
def start_collect_vibration_api(
        request_data: CollectRequest = Body(..., description="请求数据"),
        db: Session = Depends(get_db)
):
    """
    手动触发采集核心接口性能数据，并写入数据库
    实现逻辑：
    """
    result = collect_core_interface_performance(target_date=request_data.target_date, db=db, url=request_data.url)
    return result


@router.post('/calculate_jitter_exceptions', summary="计算接口抖动异常")
def calculate_jitter_exceptions_api(
        request_data: CalculateJitterRequest = Body(..., description="请求数据"),
        db: Session = Depends(get_db)
):
    """
    从core_interface_performance_detail表计算接口抖动异常，写入core_api_jitter_exception表

    实现逻辑：
    1. 从core_interface_performance_detail表读取抖动数据（变异系数>0.5）
    2. 按接口路径分组，识别连续的抖动时间段
    3. 计算每个抖动事件的开始时间、持续时长
    4. 生成详细的触发异常明细记录
    5. 批量写入core_api_jitter_exception表
    """
    result = calculate_interface_jitter_exceptions(
        db=db,
        target_date=request_data.target_date,
        url_path=request_data.url_path
    )
    return result
