"""
从采集回来抖动明细表计算接口触发开始时间和持续时长
"""

# 标准库
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from decimal import Decimal

# 第三方库
from sqlalchemy.orm import Session
from sqlalchemy import and_, tuple_

# 本地模块
from app.core.log import logger
from app.models.core_interface import CoreInterfaceORM
from app.models.core_interface_performance_detail import CoreInterfacePerformanceDetailORM
from app.models.core_api_jitter_exception import CoreApiJitterExceptionORM


def get_core_interface_vibration(db: Session):
    """
    1.查询启用状态的接口
    """
    interfaces = db.query(CoreInterfaceORM).filter(
        CoreInterfaceORM.status == 1
    ).all()
    return interfaces


def calculate_interface_jitter_exceptions(db: Session, target_date: str = None, url_path: str = None) -> dict:
    """
    抖动明细表计算接口触发开始时间和持续时长,计算结果存储在core_api_jitter_exception表

    业务背景：
    从core_interface_performance_detail表读取抖动数据，识别连续的抖动时间段，
    计算每个抖动事件的开始时间、持续时长，并记录详细的触发信息。

    实现逻辑：
    1.从core_interface_performance_detail表读取数据（变异系数>0.5的记录）
    2.按接口路径分组，按时间排序
    3.识别连续的抖动时间段（相邻记录时间间隔<=5分钟视为连续）
    4.计算每个抖动时间段的开始时间、结束时间和持续时长
    5.生成trigger_details包含抖动期间的详细数据
    6.批量写入core_api_jitter_exception表

    参数：
    - db: 数据库会话
    - target_date: 目标日期，格式如'2025-01-15'，为空则处理所有数据
    - url_path: 指定接口路径，为空则处理所有接口

    返回：
    - dict: 包含处理结果的字典
    """
    try:
        # 构建查询条件
        query = db.query(CoreInterfacePerformanceDetailORM).filter(
            CoreInterfacePerformanceDetailORM.coefficient_of_variation > 0.5
        )

        # 添加日期过滤条件
        if target_date:
            try:
                target_datetime = datetime.strptime(target_date, '%Y-%m-%d')
                start_time = target_datetime
                end_time = target_datetime + timedelta(days=1)
                query = query.filter(
                    and_(
                        CoreInterfacePerformanceDetailORM.record_time >= start_time,
                        CoreInterfacePerformanceDetailORM.record_time < end_time
                    )
                )
            except ValueError:
                logger.error(f"日期格式错误: {target_date}")
                return {"error": "日期格式错误，请使用YYYY-MM-DD格式"}

        # 添加接口路径过滤条件
        if url_path:
            query = query.filter(CoreInterfacePerformanceDetailORM.url_path == url_path)

        # 按url_path和record_time排序
        performance_records = query.order_by(
            CoreInterfacePerformanceDetailORM.url_path,
            CoreInterfacePerformanceDetailORM.record_time
        ).all()

        if not performance_records:
            logger.info("未找到符合条件的抖动数据")
            return {"inserted": 0, "msg": "未找到符合条件的抖动数据"}

        logger.info(f"找到 {len(performance_records)} 条抖动记录，开始分析抖动时间段")

        # 按接口路径分组处理
        jitter_exceptions = []
        grouped_records = _group_records_by_url_path(performance_records)

        for url_path, records in grouped_records.items():
            # 识别连续的抖动时间段
            jitter_periods = _identify_jitter_periods(records)

            # 为每个抖动时间段生成异常记录
            for period in jitter_periods:
                exception_record = _create_jitter_exception_record(period)
                jitter_exceptions.append(exception_record)

        # 批量写入数据库
        inserted_count = _batch_insert_jitter_exceptions(db, jitter_exceptions)

        logger.info(f"抖动异常计算完成，插入 {inserted_count} 条记录")

        return {
            "inserted": inserted_count,
            "processed_records": len(performance_records),
            "jitter_periods": len(jitter_exceptions),
            "msg": f"计算完成，处理 {len(performance_records)} 条抖动记录，识别 {len(jitter_exceptions)} 个抖动时间段，插入 {inserted_count} 条异常记录"
        }

    except Exception as e:
        logger.error(f"计算接口抖动异常时发生错误: {str(e)}")
        return {"error": f"计算失败: {str(e)}"}


def _group_records_by_url_path(records: List[CoreInterfacePerformanceDetailORM]) -> Dict[str, List[CoreInterfacePerformanceDetailORM]]:
    """
    按接口路径分组记录
    """
    grouped = {}
    for record in records:
        url_path = record.url_path
        if url_path not in grouped:
            grouped[url_path] = []
        grouped[url_path].append(record)
    return grouped


def _identify_jitter_periods(records: List[CoreInterfacePerformanceDetailORM]) -> List[Dict[str, Any]]:
    """
    识别连续的抖动时间段

    逻辑：
    - 相邻记录时间间隔<=5分钟视为连续抖动
    - 每个连续时间段作为一个抖动事件
    """
    if not records:
        return []

    jitter_periods = []
    current_period = {
        "url_path": records[0].url_path,
        "url_name": records[0].url_name,
        "start_time": records[0].record_time,
        "end_time": records[0].record_time,
        "records": [records[0]]
    }

    for i in range(1, len(records)):
        current_record = records[i]
        prev_record = records[i-1]

        # 计算时间间隔（分钟）
        time_diff = (current_record.record_time - prev_record.record_time).total_seconds() / 60

        # 如果时间间隔<=5分钟，视为连续抖动
        if time_diff <= 5:
            current_period["end_time"] = current_record.record_time
            current_period["records"].append(current_record)
        else:
            # 时间间隔>5分钟，结束当前抖动时间段，开始新的时间段
            jitter_periods.append(current_period)
            current_period = {
                "url_path": current_record.url_path,
                "url_name": current_record.url_name,
                "start_time": current_record.record_time,
                "end_time": current_record.record_time,
                "records": [current_record]
            }

    # 添加最后一个时间段
    jitter_periods.append(current_period)

    return jitter_periods


def _create_jitter_exception_record(period: Dict[str, Any]) -> Dict[str, Any]:
    """
    为抖动时间段创建异常记录
    """
    # 计算持续时长（毫秒）
    duration_seconds = (period["end_time"] - period["start_time"]).total_seconds()
    duration_ms = int(duration_seconds * 1000)

    # 如果只有一个记录点，设置最小持续时长为1分钟
    if duration_ms == 0:
        duration_ms = 60000  # 1分钟

    # 生成触发异常明细记录
    trigger_details = {
        "jitter_start_time": period["start_time"].isoformat(),
        "jitter_end_time": period["end_time"].isoformat(),
        "duration_ms": duration_ms,
        "record_count": len(period["records"]),
        "max_coefficient_of_variation": float(max(record.coefficient_of_variation for record in period["records"])),
        "min_coefficient_of_variation": float(min(record.coefficient_of_variation for record in period["records"])),
        "avg_coefficient_of_variation": float(sum(record.coefficient_of_variation for record in period["records"]) / len(period["records"])),
        "max_qps": max(record.total_qps for record in period["records"]),
        "min_qps": min(record.total_qps for record in period["records"]),
        "avg_qps": sum(record.total_qps for record in period["records"]) // len(period["records"]),
        "detail_records": [
            {
                "record_time": record.record_time.isoformat(),
                "coefficient_of_variation": float(record.coefficient_of_variation),
                "total_qps": record.total_qps
            }
            for record in period["records"]
        ]
    }

    return {
        "url_name": period["url_name"],
        "url_path": period["url_path"],
        "duration": duration_ms,
        "record_time": period["start_time"].date(),  # 使用开始时间的日期
        "trigger_details": trigger_details
    }


def _batch_insert_jitter_exceptions(db: Session, jitter_exceptions: List[Dict[str, Any]]) -> int:
    """
    批量插入抖动异常记录到数据库
    """
    if not jitter_exceptions:
        return 0

    # 批量查重 - 使用 url_path 和 record_time 作为唯一键
    keys = set((item["url_path"], item["record_time"]) for item in jitter_exceptions)
    existing = db.query(
        CoreApiJitterExceptionORM.url_path,
        CoreApiJitterExceptionORM.record_time
    ).filter(
        tuple_(
            CoreApiJitterExceptionORM.url_path,
            CoreApiJitterExceptionORM.record_time
        ).in_(keys)
    ).all()
    existing_set = set(existing)

    # 只插入不存在的记录
    to_insert = [
        CoreApiJitterExceptionORM(
            url_name=item["url_name"],
            url_path=item["url_path"],
            duration=item["duration"],
            record_time=item["record_time"],
            trigger_details=item["trigger_details"]
        )
        for item in jitter_exceptions
        if (item["url_path"], item["record_time"]) not in existing_set
    ]

    if not to_insert:
        logger.info("所有抖动异常记录已存在，跳过插入")
        return 0

    # 批量插入
    try:
        db.add_all(to_insert)
        db.commit()
        logger.info(f"成功插入 {len(to_insert)} 条抖动异常记录")
        return len(to_insert)
    except Exception as e:
        db.rollback()
        logger.error(f"插入抖动异常记录失败: {str(e)}")
        raise e