"""
从采集回来抖动明细表计算接口触发开始时间和持续时长
"""

# 标准库
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from decimal import Decimal

# 第三方库
from sqlalchemy.orm import Session
from sqlalchemy import and_, tuple_

# 本地模块
from app.core.log import logger
from app.models.core_interface import CoreInterfaceORM
from app.models.core_interface_performance_detail import CoreInterfacePerformanceDetailORM
from app.models.core_api_jitter_exception import CoreApiJitterExceptionORM


def get_core_interface_vibration(db: Session):
    """
    1.查询启用状态的接口
    """
    interfaces = db.query(CoreInterfaceORM).filter(
        CoreInterfaceORM.status == 1
    ).all()
    return interfaces


def calculate_interface_jitter_exceptions(db: Session, target_date: str = None, url_path: str = None) -> dict:
    """
    抖动明细表计算接口触发开始时间和持续时长,计算结果存储在core_api_jitter_exception表

    业务背景：
    从core_interface_performance_detail表读取抖动数据，逐个接口分析连续的抖动时间段，
    计算每个抖动事件的开始时间、持续时长，并记录详细的触发信息和异常值。

    优化逻辑：
    1. 一个一个接口数据进行分析（不批量处理所有接口）
    2. 分析明细过程中异常值一并记录
    3. 持续时间单位使用分钟
    4. 抖动持续时间大于等于5分钟进行记录
    5. 计算抖动增长率在异常明细字段里

    实现逻辑：
    1.从core_interface_performance_detail表读取数据（变异系数>0.5的记录）
    2.逐个接口进行分析，按时间排序
    3.识别连续的抖动时间段（相邻记录时间间隔<=5分钟视为连续）
    4.计算每个抖动时间段的开始时间、结束时间和持续时长（分钟）
    5.只记录持续时间>=5分钟的抖动事件
    6.生成trigger_details包含抖动期间的详细数据、异常值和增长率
    7.逐个写入core_api_jitter_exception表

    参数：
    - db: 数据库会话
    - target_date: 目标日期，格式如'2025-01-15'，为空则处理所有数据
    - url_path: 指定接口路径，为空则处理所有接口

    返回：
    - dict: 包含处理结果的字典
    """
    try:
        total_inserted = 0
        total_processed_interfaces = 0
        total_jitter_periods = 0

        # 获取需要处理的接口列表
        if url_path:
            # 处理指定接口
            interfaces_to_process = [url_path]
        else:
            # 获取所有有抖动数据的接口
            interfaces_query = db.query(CoreInterfacePerformanceDetailORM.url_path).filter(
                CoreInterfacePerformanceDetailORM.coefficient_of_variation > 0.5
            )

            # 添加日期过滤条件
            if target_date:
                try:
                    target_datetime = datetime.strptime(target_date, '%Y-%m-%d')
                    start_time = target_datetime
                    end_time = target_datetime + timedelta(days=1)
                    interfaces_query = interfaces_query.filter(
                        and_(
                            CoreInterfacePerformanceDetailORM.record_time >= start_time,
                            CoreInterfacePerformanceDetailORM.record_time < end_time
                        )
                    )
                except ValueError:
                    logger.error(f"日期格式错误: {target_date}")
                    return {"error": "日期格式错误，请使用YYYY-MM-DD格式"}

            interfaces_to_process = [row[0] for row in interfaces_query.distinct().all()]

        if not interfaces_to_process:
            logger.info("未找到符合条件的接口")
            return {"inserted": 0, "msg": "未找到符合条件的接口"}

        logger.info(f"开始逐个分析 {len(interfaces_to_process)} 个接口的抖动数据")

        # 逐个接口进行分析
        for current_url_path in interfaces_to_process:
            logger.info(f"正在分析接口: {current_url_path}")

            # 处理单个接口的抖动数据
            interface_result = _process_single_interface_jitter(
                db, current_url_path, target_date
            )

            total_inserted += interface_result["inserted"]
            total_jitter_periods += interface_result["jitter_periods"]
            total_processed_interfaces += 1

            logger.info(f"接口 {current_url_path} 处理完成: 插入 {interface_result['inserted']} 条记录")

        logger.info(f"所有接口抖动异常计算完成，总计插入 {total_inserted} 条记录")

        return {
            "inserted": total_inserted,
            "processed_interfaces": total_processed_interfaces,
            "jitter_periods": total_jitter_periods,
            "msg": f"计算完成，处理 {total_processed_interfaces} 个接口，识别 {total_jitter_periods} 个抖动时间段，插入 {total_inserted} 条异常记录"
        }

    except Exception as e:
        logger.error(f"计算接口抖动异常时发生错误: {str(e)}")
        return {"error": f"计算失败: {str(e)}"}


def _process_single_interface_jitter(db: Session, url_path: str, target_date: str = None) -> dict:
    """
    处理单个接口的抖动数据分析

    参数：
    - db: 数据库会话
    - url_path: 接口路径
    - target_date: 目标日期

    返回：
    - dict: 包含处理结果
    """
    try:
        # 查询该接口的抖动数据
        query = db.query(CoreInterfacePerformanceDetailORM).filter(
            and_(
                CoreInterfacePerformanceDetailORM.url_path == url_path,
                CoreInterfacePerformanceDetailORM.coefficient_of_variation > 0.5
            )
        )

        # 添加日期过滤条件
        if target_date:
            target_datetime = datetime.strptime(target_date, '%Y-%m-%d')
            start_time = target_datetime
            end_time = target_datetime + timedelta(days=1)
            query = query.filter(
                and_(
                    CoreInterfacePerformanceDetailORM.record_time >= start_time,
                    CoreInterfacePerformanceDetailORM.record_time < end_time
                )
            )

        # 按时间排序
        performance_records = query.order_by(
            CoreInterfacePerformanceDetailORM.record_time
        ).all()

        if not performance_records:
            return {"inserted": 0, "jitter_periods": 0}

        logger.info(f"接口 {url_path} 找到 {len(performance_records)} 条抖动记录")

        # 识别连续的抖动时间段
        jitter_periods = _identify_jitter_periods_optimized(performance_records)

        # 过滤持续时间>=5分钟的抖动事件
        valid_jitter_periods = [
            period for period in jitter_periods
            if period["duration_minutes"] >= 5
        ]

        if not valid_jitter_periods:
            logger.info(f"接口 {url_path} 没有持续时间>=5分钟的抖动事件")
            return {"inserted": 0, "jitter_periods": 0}

        logger.info(f"接口 {url_path} 识别到 {len(valid_jitter_periods)} 个有效抖动时间段")

        # 逐个写入抖动异常记录
        inserted_count = 0
        for period in valid_jitter_periods:
            exception_record = _create_jitter_exception_record_optimized(period)
            if _insert_single_jitter_exception(db, exception_record):
                inserted_count += 1

        return {
            "inserted": inserted_count,
            "jitter_periods": len(valid_jitter_periods)
        }

    except Exception as e:
        logger.error(f"处理接口 {url_path} 抖动数据时发生错误: {str(e)}")
        return {"inserted": 0, "jitter_periods": 0}


def _identify_jitter_periods_optimized(records: List[CoreInterfacePerformanceDetailORM]) -> List[Dict[str, Any]]:
    """
    识别连续的抖动时间段（优化版本）

    逻辑：
    - 相邻记录时间间隔<=5分钟视为连续抖动
    - 每个连续时间段作为一个抖动事件
    - 计算持续时间（分钟）
    - 识别异常值和增长率
    """
    if not records:
        return []

    jitter_periods = []
    current_period = {
        "url_path": records[0].url_path,
        "url_name": records[0].url_name,
        "start_time": records[0].record_time,
        "end_time": records[0].record_time,
        "records": [records[0]]
    }

    for i in range(1, len(records)):
        current_record = records[i]
        prev_record = records[i-1]

        # 计算时间间隔（分钟）
        time_diff = (current_record.record_time - prev_record.record_time).total_seconds() / 60

        # 如果时间间隔<=5分钟，视为连续抖动
        if time_diff <= 5:
            current_period["end_time"] = current_record.record_time
            current_period["records"].append(current_record)
        else:
            # 时间间隔>5分钟，结束当前抖动时间段，开始新的时间段
            # 计算当前时间段的持续时间和其他指标
            _calculate_period_metrics(current_period)
            jitter_periods.append(current_period)

            current_period = {
                "url_path": current_record.url_path,
                "url_name": current_record.url_name,
                "start_time": current_record.record_time,
                "end_time": current_record.record_time,
                "records": [current_record]
            }

    # 处理最后一个时间段
    _calculate_period_metrics(current_period)
    jitter_periods.append(current_period)

    return jitter_periods


def _calculate_period_metrics(period: Dict[str, Any]) -> None:
    """
    计算抖动时间段的各项指标

    包括：
    - 持续时间（分钟）
    - 异常值识别
    - 抖动增长率
    - 统计指标
    """
    records = period["records"]

    # 计算持续时间（分钟）
    duration_seconds = (period["end_time"] - period["start_time"]).total_seconds()
    duration_minutes = duration_seconds / 60

    # 如果只有一个记录点，设置最小持续时长为1分钟
    if duration_minutes == 0:
        duration_minutes = 1

    period["duration_minutes"] = duration_minutes

    # 提取变异系数和QPS数据
    cv_values = [float(record.coefficient_of_variation) for record in records]
    qps_values = [record.total_qps for record in records]

    # 计算统计指标
    period["max_cv"] = max(cv_values)
    period["min_cv"] = min(cv_values)
    period["avg_cv"] = sum(cv_values) / len(cv_values)
    period["max_qps"] = max(qps_values)
    period["min_qps"] = min(qps_values)
    period["avg_qps"] = sum(qps_values) / len(qps_values)

    # 识别异常值（使用3σ原则）
    if len(cv_values) > 2:
        cv_mean = period["avg_cv"]
        cv_std = (sum((x - cv_mean) ** 2 for x in cv_values) / len(cv_values)) ** 0.5
        threshold = cv_mean + 3 * cv_std

        period["outliers"] = [
            {
                "record_time": record.record_time.isoformat(),
                "coefficient_of_variation": float(record.coefficient_of_variation),
                "total_qps": record.total_qps,
                "is_outlier": float(record.coefficient_of_variation) > threshold
            }
            for record in records
        ]
    else:
        period["outliers"] = [
            {
                "record_time": record.record_time.isoformat(),
                "coefficient_of_variation": float(record.coefficient_of_variation),
                "total_qps": record.total_qps,
                "is_outlier": False
            }
            for record in records
        ]

    # 计算抖动增长率（第一个记录到最后一个记录的变化率）
    if len(cv_values) > 1:
        first_cv = cv_values[0]
        last_cv = cv_values[-1]
        if first_cv > 0:
            growth_rate = ((last_cv - first_cv) / first_cv) * 100
        else:
            growth_rate = 0
    else:
        growth_rate = 0

    period["jitter_growth_rate"] = round(growth_rate, 2)


def _create_jitter_exception_record_optimized(period: Dict[str, Any]) -> Dict[str, Any]:
    """
    为抖动时间段创建异常记录（优化版本）

    优化内容：
    - 持续时间使用分钟
    - 包含异常值分析
    - 包含抖动增长率
    - 详细的触发异常明细记录
    """
    # 持续时长（分钟）
    duration_minutes = int(period["duration_minutes"])

    # 生成触发异常明细记录
    trigger_details = {
        "jitter_start_time": period["start_time"].isoformat(),
        "jitter_end_time": period["end_time"].isoformat(),
        "duration_minutes": duration_minutes,
        "record_count": len(period["records"]),
        "max_coefficient_of_variation": period["max_cv"],
        "min_coefficient_of_variation": period["min_cv"],
        "avg_coefficient_of_variation": round(period["avg_cv"], 4),
        "max_qps": period["max_qps"],
        "min_qps": period["min_qps"],
        "avg_qps": round(period["avg_qps"], 2),
        "jitter_growth_rate": period["jitter_growth_rate"],
        "outliers_analysis": {
            "total_outliers": sum(1 for item in period["outliers"] if item["is_outlier"]),
            "outlier_ratio": round(sum(1 for item in period["outliers"] if item["is_outlier"]) / len(period["outliers"]) * 100, 2),
            "outlier_details": [item for item in period["outliers"] if item["is_outlier"]]
        },
        "detail_records": period["outliers"]  # 包含所有记录和异常值标记
    }

    return {
        "url_name": period["url_name"],
        "url_path": period["url_path"],
        "duration": duration_minutes,  # 使用分钟作为单位
        "record_time": period["start_time"].date(),  # 使用开始时间的日期
        "trigger_details": trigger_details
    }


def _insert_single_jitter_exception(db: Session, exception_record: Dict[str, Any]) -> bool:
    """
    插入单个抖动异常记录到数据库

    返回：
    - bool: 是否成功插入
    """
    try:
        # 检查是否已存在相同记录
        existing = db.query(CoreApiJitterExceptionORM).filter(
            and_(
                CoreApiJitterExceptionORM.url_path == exception_record["url_path"],
                CoreApiJitterExceptionORM.record_time == exception_record["record_time"]
            )
        ).first()

        if existing:
            logger.info(f"抖动异常记录已存在: {exception_record['url_path']} - {exception_record['record_time']}")
            return False

        # 创建新记录
        new_record = CoreApiJitterExceptionORM(
            url_name=exception_record["url_name"],
            url_path=exception_record["url_path"],
            duration=exception_record["duration"],
            record_time=exception_record["record_time"],
            trigger_details=exception_record["trigger_details"]
        )

        db.add(new_record)
        db.commit()

        logger.info(f"成功插入抖动异常记录: {exception_record['url_path']} - {exception_record['record_time']}")
        return True

    except Exception as e:
        db.rollback()
        logger.error(f"插入抖动异常记录失败: {str(e)}")
        return False


