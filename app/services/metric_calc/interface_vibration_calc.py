"""
从采集回来抖动明细表计算接口触发开始时间和持续时长
"""

# 标准库
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from decimal import Decimal

# 第三方库
from sqlalchemy.orm import Session
from sqlalchemy import and_, tuple_

# 本地模块
from app.core.log import logger
from app.models.core_interface import CoreInterfaceORM
from app.models.core_interface_performance_detail import CoreInterfacePerformanceDetailORM
from app.models.core_api_jitter_exception import CoreApiJitterExceptionORM


def get_core_interface_vibration(db: Session):
    """
    1.查询启用状态的接口
    """
    interfaces = db.query(CoreInterfaceORM).filter(
        CoreInterfaceORM.status == 1
    ).all()
    return interfaces


def calculate_interface_jitter_exceptions(db: Session, target_date: str = None, url_path: str = None) -> dict:
    """
    抖动明细表计算接口触发开始时间和持续时长,计算结果存储在core_api_jitter_exception表

    业务背景：
    从core_interface_performance_detail表读取抖动数据，逐个接口分析连续的抖动时间段，
    计算每个抖动事件的开始时间、持续时长，并记录详细的触发信息和异常值。

    优化逻辑：
    1. 一个一个接口数据进行分析（不批量处理所有接口）
    2. 分析明细过程中异常值一并记录
    3. 持续时间单位使用分钟
    4. 抖动持续时间大于等于5分钟进行记录
    5. 计算抖动增长率在异常明细字段里

    实现逻辑：
    1.从core_interface_performance_detail表读取数据（变异系数>0.5的记录）
    2.逐个接口进行分析，按时间排序
    3.识别连续的抖动时间段（相邻记录时间间隔<=5分钟视为连续）
    4.计算每个抖动时间段的开始时间、结束时间和持续时长（分钟）
    5.只记录持续时间>=5分钟的抖动事件
    6.生成trigger_details包含抖动期间的详细数据、异常值和增长率
    7.逐个写入core_api_jitter_exception表

    参数：
    - db: 数据库会话
    - target_date: 目标日期，格式如'2025-01-15'，为空则处理所有数据
    - url_path: 指定接口路径，为空则处理所有接口

    返回：
    - dict: 包含处理结果的字典
    """
    try:
        total_inserted = 0
        total_processed_interfaces = 0
        total_jitter_periods = 0

        # 获取需要处理的接口列表
        if url_path:
            # 处理指定接口
            interfaces_to_process = [url_path]
        else:
            # 获取所有有抖动数据的接口
            interfaces_query = db.query(CoreInterfacePerformanceDetailORM.url_path).filter(
                CoreInterfacePerformanceDetailORM.coefficient_of_variation > 0.5
            )

            # 添加日期过滤条件
            if target_date:
                try:
                    target_datetime = datetime.strptime(target_date, '%Y-%m-%d')
                    start_time = target_datetime
                    end_time = target_datetime + timedelta(days=1)
                    interfaces_query = interfaces_query.filter(
                        and_(
                            CoreInterfacePerformanceDetailORM.record_time >= start_time,
                            CoreInterfacePerformanceDetailORM.record_time < end_time
                        )
                    )
                except ValueError:
                    logger.error(f"日期格式错误: {target_date}")
                    return {"error": "日期格式错误，请使用YYYY-MM-DD格式"}

            interfaces_to_process = [row[0] for row in interfaces_query.distinct().all()]

        if not interfaces_to_process:
            logger.info("未找到符合条件的接口")
            return {"inserted": 0, "msg": "未找到符合条件的接口"}

        logger.info(f"开始逐个分析 {len(interfaces_to_process)} 个接口的抖动数据")

        # 逐个接口进行分析
        for current_url_path in interfaces_to_process:
            logger.info(f"正在分析接口: {current_url_path}")

            # 处理单个接口的抖动数据
            interface_result = _process_single_interface_jitter(
                db, current_url_path, target_date
            )

            total_inserted += interface_result["inserted"]
            total_jitter_periods += interface_result["jitter_periods"]
            total_processed_interfaces += 1

            logger.info(f"接口 {current_url_path} 处理完成: 插入 {interface_result['inserted']} 条记录")

        logger.info(f"所有接口抖动异常计算完成，总计插入 {total_inserted} 条记录")

        return {
            "inserted": total_inserted,
            "processed_interfaces": total_processed_interfaces,
            "jitter_periods": total_jitter_periods,
            "msg": f"计算完成，处理 {total_processed_interfaces} 个接口，识别 {total_jitter_periods} 个抖动时间段，插入 {total_inserted} 条异常记录"
        }

    except Exception as e:
        logger.error(f"计算接口抖动异常时发生错误: {str(e)}")
        return {"error": f"计算失败: {str(e)}"}


def _group_records_by_url_path(records: List[CoreInterfacePerformanceDetailORM]) -> Dict[str, List[CoreInterfacePerformanceDetailORM]]:
    """
    按接口路径分组记录
    """
    grouped = {}
    for record in records:
        url_path = record.url_path
        if url_path not in grouped:
            grouped[url_path] = []
        grouped[url_path].append(record)
    return grouped


def _identify_jitter_periods(records: List[CoreInterfacePerformanceDetailORM]) -> List[Dict[str, Any]]:
    """
    识别连续的抖动时间段

    逻辑：
    - 相邻记录时间间隔<=5分钟视为连续抖动
    - 每个连续时间段作为一个抖动事件
    """
    if not records:
        return []

    jitter_periods = []
    current_period = {
        "url_path": records[0].url_path,
        "url_name": records[0].url_name,
        "start_time": records[0].record_time,
        "end_time": records[0].record_time,
        "records": [records[0]]
    }

    for i in range(1, len(records)):
        current_record = records[i]
        prev_record = records[i-1]

        # 计算时间间隔（分钟）
        time_diff = (current_record.record_time - prev_record.record_time).total_seconds() / 60

        # 如果时间间隔<=5分钟，视为连续抖动
        if time_diff <= 5:
            current_period["end_time"] = current_record.record_time
            current_period["records"].append(current_record)
        else:
            # 时间间隔>5分钟，结束当前抖动时间段，开始新的时间段
            jitter_periods.append(current_period)
            current_period = {
                "url_path": current_record.url_path,
                "url_name": current_record.url_name,
                "start_time": current_record.record_time,
                "end_time": current_record.record_time,
                "records": [current_record]
            }

    # 添加最后一个时间段
    jitter_periods.append(current_period)

    return jitter_periods


def _create_jitter_exception_record(period: Dict[str, Any]) -> Dict[str, Any]:
    """
    为抖动时间段创建异常记录
    """
    # 计算持续时长（毫秒）
    duration_seconds = (period["end_time"] - period["start_time"]).total_seconds()
    duration_ms = int(duration_seconds * 1000)

    # 如果只有一个记录点，设置最小持续时长为1分钟
    if duration_ms == 0:
        duration_ms = 60000  # 1分钟

    # 生成触发异常明细记录
    trigger_details = {
        "jitter_start_time": period["start_time"].isoformat(),
        "jitter_end_time": period["end_time"].isoformat(),
        "duration_ms": duration_ms,
        "record_count": len(period["records"]),
        "max_coefficient_of_variation": float(max(record.coefficient_of_variation for record in period["records"])),
        "min_coefficient_of_variation": float(min(record.coefficient_of_variation for record in period["records"])),
        "avg_coefficient_of_variation": float(sum(record.coefficient_of_variation for record in period["records"]) / len(period["records"])),
        "max_qps": max(record.total_qps for record in period["records"]),
        "min_qps": min(record.total_qps for record in period["records"]),
        "avg_qps": sum(record.total_qps for record in period["records"]) // len(period["records"]),
        "detail_records": [
            {
                "record_time": record.record_time.isoformat(),
                "coefficient_of_variation": float(record.coefficient_of_variation),
                "total_qps": record.total_qps
            }
            for record in period["records"]
        ]
    }

    return {
        "url_name": period["url_name"],
        "url_path": period["url_path"],
        "duration": duration_ms,
        "record_time": period["start_time"].date(),  # 使用开始时间的日期
        "trigger_details": trigger_details
    }


def _batch_insert_jitter_exceptions(db: Session, jitter_exceptions: List[Dict[str, Any]]) -> int:
    """
    批量插入抖动异常记录到数据库
    """
    if not jitter_exceptions:
        return 0

    # 批量查重 - 使用 url_path 和 record_time 作为唯一键
    keys = set((item["url_path"], item["record_time"]) for item in jitter_exceptions)
    existing = db.query(
        CoreApiJitterExceptionORM.url_path,
        CoreApiJitterExceptionORM.record_time
    ).filter(
        tuple_(
            CoreApiJitterExceptionORM.url_path,
            CoreApiJitterExceptionORM.record_time
        ).in_(keys)
    ).all()
    existing_set = set(existing)

    # 只插入不存在的记录
    to_insert = [
        CoreApiJitterExceptionORM(
            url_name=item["url_name"],
            url_path=item["url_path"],
            duration=item["duration"],
            record_time=item["record_time"],
            trigger_details=item["trigger_details"]
        )
        for item in jitter_exceptions
        if (item["url_path"], item["record_time"]) not in existing_set
    ]

    if not to_insert:
        logger.info("所有抖动异常记录已存在，跳过插入")
        return 0

    # 批量插入
    try:
        db.add_all(to_insert)
        db.commit()
        logger.info(f"成功插入 {len(to_insert)} 条抖动异常记录")
        return len(to_insert)
    except Exception as e:
        db.rollback()
        logger.error(f"插入抖动异常记录失败: {str(e)}")
        raise e