"""
核心接口管理业务服务
"""

import re
from urllib.parse import urlparse
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models.core_interface import CoreInterfaceORM
from app.core.log import logger


class CoreInterfaceService:
    """核心接口管理业务服务类"""

    @staticmethod
    def clean_path_fingerprint(path: str) -> str:
        """
        清洗路径指纹，将动态参数替换为 * 号

        例如：
        - /api/user/123/profile -> /api/user/*/profile
        - /api/order/abc123/detail -> /api/order/*/detail
        - /a/123/a -> /a/*/a
        """
        if not path:
            return path

        # 匹配数字、字母数字组合、UUID等动态参数
        patterns = [
            r'/\d+/',  # 纯数字 /123/
            r'/[a-zA-Z0-9]+/',  # 字母数字组合 /abc123/
            r'/[a-f0-9-]{36}/',  # UUID格式
            r'/[a-f0-9-]{32}/',  # MD5格式
        ]

        cleaned_path = path
        for pattern in patterns:
            cleaned_path = re.sub(pattern, '/*//', cleaned_path)

        # 处理路径末尾的动态参数
        end_patterns = [
            r'/\d+$',  # 以数字结尾 /123
            r'/[a-zA-Z0-9]+$',  # 以字母数字组合结尾 /abc123
            r'/[a-f0-9-]{36}$',  # 以UUID结尾
            r'/[a-f0-9-]{32}$',  # 以MD5结尾
        ]

        for pattern in end_patterns:
            cleaned_path = re.sub(pattern, '/*', cleaned_path)

        return cleaned_path

    @staticmethod
    def extract_path_from_url(url: str) -> str:
        """
        从完整URL中提取路径部分

        例如：
        - https://api.example.com/user/123 -> /user/123
        - http://localhost:8080/api/v1/test -> /api/v1/test
        """
        if not url:
            return url

        # 如果是完整URL（以http://或https://开头）
        if url.startswith(('http://', 'https://')):
            parsed = urlparse(url)
            return parsed.path

        # 如果已经是路径格式，直接返回
        return url

    def add_core_interface(self, interface_name: str, url: str, product_type: str, db: Session) -> Dict[str, Any]:
        """
        新增核心接口

        业务逻辑：
        1. 如果URL是完整URL（https开头），自动提取path
        2. 对path进行指纹清洗，替换动态参数为*号
        3. 检查是否重复（基于清洗后的URL）
        4. 新增记录

        参数：
        - interface_name: 接口名称
        - url: 接口URL或路径
        - product_type: 产品类型
        - db: 数据库会话

        返回：
        - Dict: 包含操作结果
        """
        try:
            # 1. 提取路径（如果是完整URL）
            path = self.extract_path_from_url(url)

            # 2. 清洗路径指纹
            cleaned_path = self.clean_path_fingerprint(path)

        logger.info(f"原始URL: {url} -> 提取路径: {path} -> 清洗后: {cleaned_path}")

        # 3. 检查是否重复（基于清洗后的URL）
        existing = db.query(CoreInterfaceORM).filter(
            CoreInterfaceORM.url == cleaned_path
        ).first()

        if existing:
            return {
                "code": -1,
                "msg": f"接口已存在，清洗后的路径: {cleaned_path}",
                "data": None
            }

        # 4. 创建新记录
        new_interface = CoreInterfaceORM(
            interface_name=interface_name,
            url=cleaned_path,  # 存储清洗后的路径
            product_type=product_type,
            status=1  # 默认启用
        )

        db.add(new_interface)
        db.commit()
        db.refresh(new_interface)

        logger.info(f"成功新增核心接口: {interface_name} - {cleaned_path}")

        return {
            "code": 0,
            "msg": "新增成功",
            "data": {
                "id": new_interface.id,
                "interface_name": new_interface.interface_name,
                "url": new_interface.url,
                "product_type": new_interface.product_type,
                "original_url": url,
                "cleaned_url": cleaned_path
            }
        }

         except Exception as e:
            db.rollback()
            logger.error(f"新增核心接口失败: {str(e)}")
            return {
                "code": -1,
                "msg": f"新增失败: {str(e)}",
                "data": None
            }


def update_core_interface(self, interface_id: int, interface_name: str, db: Session) -> Dict[str, Any]:


    """
    编辑核心接口（只能编辑名称）

    业务逻辑：
    1. 根据ID查找接口
    2. 只允许修改接口名称
    3. 更新记录

    参数：
    - interface_id: 接口ID
    - interface_name: 新的接口名称
    - db: 数据库会话

    返回：
    - Dict: 包含操作结果
    """
try:
    # 查找接口
    interface = db.query(CoreInterfaceORM).filter(
        CoreInterfaceORM.id == interface_id
    ).first()

    if not interface:
        return {
            "code": -1,
            "msg": f"接口不存在，ID: {interface_id}",
            "data": None
        }

    # 更新接口名称
    old_name = interface.interface_name
    interface.interface_name = interface_name

    db.commit()
    db.refresh(interface)

    logger.info(f"成功更新核心接口名称: {old_name} -> {interface_name} (ID: {interface_id})")

    return {
        "code": 0,
        "msg": "更新成功",
        "data": {
            "id": interface.id,
            "interface_name": interface.interface_name,
            "url": interface.url,
            "product_type": interface.product_type,
            "status": interface.status,
            "old_name": old_name
        }
    }

except Exception as e:
    db.rollback()
    logger.error(f"更新核心接口失败: {str(e)}")
    return {
        "code": -1,
        "msg": f"更新失败: {str(e)}",
        "data": None
    }


def delete_core_interface(self, interface_id: int, db: Session) -> Dict[str, Any]:


    """
    删除核心接口

    业务逻辑：
    1. 根据ID查找接口
    2. 删除记录

    参数：
    - interface_id: 接口ID
    - db: 数据库会话

    返回：
    - Dict: 包含操作结果
    """
try:
    # 查找接口
    interface = db.query(CoreInterfaceORM).filter(
        CoreInterfaceORM.id == interface_id
    ).first()

    if not interface:
        return {
            "code": -1,
            "msg": f"接口不存在，ID: {interface_id}",
            "data": None
        }

    # 保存删除前的信息
    deleted_info = {
        "id": interface.id,
        "interface_name": interface.interface_name,
        "url": interface.url,
        "product_type": interface.product_type
    }

    # 删除记录
    db.delete(interface)
    db.commit()

    logger.info(f"成功删除核心接口: {interface.interface_name} - {interface.url} (ID: {interface_id})")

    return {
        "code": 0,
        "msg": "删除成功",
        "data": deleted_info
    }

except Exception as e:
    db.rollback()
    logger.error(f"删除核心接口失败: {str(e)}")
    return {
        "code": -1,
        "msg": f"删除失败: {str(e)}",
        "data": None
    }


def get_core_interface_list(self, db: Session, page: int = 1, page_size: int = 20) -> Dict[str, Any]:


    """
    获取核心接口列表

    参数：
    - db: 数据库会话
    - page: 页码
    - page_size: 每页数量

    返回：
    - Dict: 包含接口列表和分页信息
    """
try:
    # 计算偏移量
    offset = (page - 1) * page_size

    # 查询总数
    total = db.query(CoreInterfaceORM).count()

    # 查询列表
    interfaces = db.query(CoreInterfaceORM).offset(offset).limit(page_size).all()

    # 转换为字典格式
    interface_list = []
    for interface in interfaces:
        interface_list.append({
            "id": interface.id,
            "interface_name": interface.interface_name,
            "url": interface.url,
            "product_type": interface.product_type,
            "status": interface.status,
            "create_time": interface.create_time.isoformat() if interface.create_time else None,
            "update_time": interface.update_time.isoformat() if interface.update_time else None
        })

    return {
        "code": 0,
        "msg": "查询成功",
        "data": {
            "list": interface_list,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total,
                "total_pages": (total + page_size - 1) // page_size
            }
        }
    }

except Exception as e:
    logger.error(f"查询核心接口列表失败: {str(e)}")
    return {
        "code": -1,
        "msg": f"查询失败: {str(e)}",
        "data": None
    }


def get_core_interface_by_id(self, interface_id: int, db: Session) -> Dict[str, Any]:


    """
    根据ID获取核心接口详情

    参数：
    - interface_id: 接口ID
    - db: 数据库会话

    返回：
    - Dict: 包含接口详情
    """
try:
    interface = db.query(CoreInterfaceORM).filter(
        CoreInterfaceORM.id == interface_id
    ).first()

    if not interface:
        return {
            "code": -1,
            "msg": f"接口不存在，ID: {interface_id}",
            "data": None
        }

    return {
        "code": 0,
        "msg": "查询成功",
        "data": {
            "id": interface.id,
            "interface_name": interface.interface_name,
            "url": interface.url,
            "product_type": interface.product_type,
            "status": interface.status,
            "create_time": interface.create_time.isoformat() if interface.create_time else None,
            "update_time": interface.update_time.isoformat() if interface.update_time else None
        }
    }

except Exception as e:
    logger.error(f"查询核心接口详情失败: {str(e)}")
    return {
        "code": -1,
        "msg": f"查询失败: {str(e)}",
        "data": None
    }
